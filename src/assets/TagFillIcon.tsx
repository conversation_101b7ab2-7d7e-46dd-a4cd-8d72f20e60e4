import React from 'react';

type TTagFillIconProps = {
  color?: string;
  size?: number;
};
export const TagFillIcon: React.FC<TTagFillIconProps> = ({ ...props }: TTagFillIconProps) => {
  const { color, size = 12 } = props;
  return (
    <div
      className="rounded-full"
      style={{
        backgroundColor: color?.includes('#') ? color : '#8F5CFF', // big360Color.brand['400']
        width: `${size}px`,
        height: `${size}px`,
      }}
    />
  );
};
