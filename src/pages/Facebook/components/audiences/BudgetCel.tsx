import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { formatCurrency } from '@/utils/number';
import { toSimplest } from '@/utils/string';
import { RiCloseLine, RiEdit2Line, RiLoader2Line } from '@remixicon/react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from '@/hooks/use-toast';

export type BudgetCellProps = {
  value: string | null;
  onChangeBudget: (value: string) => Promise<boolean>;
  popoverStates?: Record<string, boolean>;
  setPopoverStates?: (states: Record<string, boolean> | ((prev: Record<string, boolean>) => Record<string, boolean>)) => void;
  refreshingRows?: Set<string>;
  setRefreshingRows?: (rows: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
  rowId?: string;
};

const BudgetCel = ({
  value,
  onChangeBudget,
  popoverStates,
  setPopoverStates,
  rowId
}: BudgetCellProps) => {
  const popoverKey = rowId ? `${rowId}-budget` : `budget-${Date.now()}`;
  const [localOpen, setLocalOpen] = useState<boolean>(false);
  const open = popoverStates?.[popoverKey] ?? localOpen;
  const setOpen = setPopoverStates
    ? (isOpen: boolean) => setPopoverStates(prev => ({ ...prev, [popoverKey]: isOpen }))
    : setLocalOpen;

  const [budget, setBudget] = useState('');
  const [budgetValue, setBudgetValue] = useState(value);
  const [loading, setLoading] = useState(false);

  const { t } = useTranslation();
  const toggle = () => setOpen(!open);

  const handleChangeBudget = async () => {
    const numericBudget = Number(budget);
    if (numericBudget < 100) {
      toast({
        status: 'error',
        description: t('common.facebookAds.campaign.budgetUnderLimit'),
      });
      return;
    }
    setLoading(true);
    const result = await onChangeBudget(budget);
    if (result) {
      toggle();
      setBudgetValue(budget);
      setBudget('');
    }
    setLoading(false);
  };

  const formattedBudget = (() => {
    if (value) {
      return formatCurrency(Number(value), 'USD');
    }
    if (budget === '0') {
      return '';
    }

    const formatted = formatCurrency(Number(budget), 'USD');
    return formatted === '0' ? '' : formatted;
  })();
  return (
    <div className="font-medium flex items-center gap-2">
      {value ? (
        <>
          <span>{formatCurrency(Number(budgetValue), 'USD')}</span>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button className="rounded-xl" size={'icon'} variant={'outline'}>
                <RiEdit2Line />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="max-w-[297.5px] flex flex-col gap-2 px-3 py-2 rounded-2xl">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {t('common.facebookAds.campaign.budget')}
                </span>
                <button onClick={toggle}>
                  <RiCloseLine size={20} />
                </button>
              </div>
              <input
                className="outline-none border h-10 w-full p-3 rounded-xl text-sm"
                placeholder={t('common.facebookAds.campaign.enterBudget')}
                value={formattedBudget}
                onChange={(e) => setBudget(toSimplest(e.target.value))}
              />
              <Button
                className="ml-auto rounded-md"
                size={'sm'}
                onClick={handleChangeBudget}
                disabled={loading}
              >
                {loading ? (
                  <RiLoader2Line className="animate-spin" size={24} />
                ) : (
                  t('common.button.save')
                )}
              </Button>
            </PopoverContent>
          </Popover>
        </>
      ) : (
        '--'
      )}
    </div>
  );
};

export default BudgetCel;
