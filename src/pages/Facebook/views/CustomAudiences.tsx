import { useContext, useEffect, useState, useRef } from 'react';
import FilterPanel from '../components/FilterPanel';
import DataTable from '@/components/table/DataTable';
import audienceCol from '../components/colums/audienceCol';
import { AudienceContext } from '../context/AudienceContext';
import { IFbPage, TFilterAudience } from '@/types/facebook';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { IAudienceDetail } from '@/types/audience';

interface ICustomAudiences {
  filterPayload: TFilterAudience;
  setFilterPayload: (value: TFilterAudience) => void;
  listPages: IFbPage[];
}

const CustomAudiences = ({ filterPayload, setFilterPayload, listPages }: ICustomAudiences) => {
  const [ad_Account_id, setAd_Account_id] = useState<string>('');
  const { items, count, loading, handleGetAudience } = useContext(AudienceContext);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(items);
  const [idActive, setIdActive] = useState<number>(0);
  const [loadingDetailIds, setLoadingDetailIds] = useState<Set<number>>(new Set());
  const fetchedDetailIds = useRef<Set<number>>(new Set());

  // Popover state management
  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());

  const currentFbAccount = listPages.find((item) => item.selected);

  useEffect(() => {
    if (!!items.length) {
      setListAudiences(items);
    }
  }, [items]);

  useEffect(() => {
    if (!ad_Account_id) {
      return;
    }
    handleGetAudience({
      ...filterPayload,
      ad_account_id: ad_Account_id,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterPayload, ad_Account_id]);

  useEffect(() => {
    if (!listPages || !currentFbAccount) {
      return;
    }
    setAd_Account_id(currentFbAccount.id);
  }, [currentFbAccount, listPages]);

  useEffect(() => {
    if (idActive > 0 && !loadingDetailIds.has(idActive) && !fetchedDetailIds.current.has(idActive)) {
      // Find the specific item with this job_id
      const targetItem = listAudiences.find((item) => item.job_id === idActive);

      // Only fetch if this specific item needs updating
      if (targetItem && (targetItem.total_records === 0 || targetItem.status === 'PENDING')) {
        fetchedDetailIds.current.add(idActive); // Mark as being fetched
        setLoadingDetailIds(prev => new Set(prev).add(idActive));

        get({
          endpoint: ENDPOINTS.fb.log_detail(idActive),
        }).then((res) => {
          const dataAudience = res?.data?.data as unknown as IAudienceDetail;
          setListAudiences((prev) =>
            prev.map((item) =>
              item.job_id === dataAudience.job_id? dataAudience : item,
            ),
          );
        }).catch((error) => {
          console.error('Error fetching Facebook audience detail:', error);
          fetchedDetailIds.current.delete(idActive);
        }).finally(() => {
          /* eslint-disable @typescript-eslint/no-explicit-any */
          /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */
          /* @ts-expect-error */
          setLoadingDetailIds((prev: any) => {
            const newSet = new Set(prev);
            newSet.delete(idActive);
          });
        });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({ ...filterPayload, ...value });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={listAudiences}
        columns={audienceCol({ act: currentFbAccount?.account_id || '', setIdActive })}
        total={count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
    </div>
  );
};
export default CustomAudiences;
