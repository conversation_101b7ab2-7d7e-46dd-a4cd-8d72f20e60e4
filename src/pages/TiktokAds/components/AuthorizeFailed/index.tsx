import { RiAlertLine } from '@remixicon/react';
import { Button } from '@/components/ui/button';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export const AuthorizeFailed = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <div className="max-w-[682px] w-full mt-[90px] flex items-center justify-center mx-auto">
      <div className="flex items-center justify-center flex-col">
        <div className="w-[112px] h-[112px] bg-big360Color-danger-50 rounded-full flex items-center justify-center mx-auto mb-6">
          <RiAlertLine size={48} className="text-big360Color-danger-600" />
        </div>

        <h1 className="text-lg text-big360Color-neutral-950 font-semibold mb-2">
          {t('tiktokAds.somethingWentWrong')}
        </h1>

        <p className="text-base text-big360Color-neutral-700 mb-8 px-5 text-center">
          {t('tiktokAds.somethingWentWrongDescription')}
        </p>

        <Button
          onClick={() => {
            navigate(`${ROOT_PATH}/${ROOT_ROUTE.tiktok['']}`);
          }}
          variant={'primary'}
          className="w-fit"
        >
          {t('tiktokAds.reConnect')}
        </Button>
      </div>
    </div>
  );
};
