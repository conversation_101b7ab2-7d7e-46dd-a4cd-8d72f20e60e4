import { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { deleteStorage, setStorage } from '@/utils/asyncStorage';
import { TIKTOK_STORAGE_KEY } from '@/constants/facebook';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { IAdsAccount, ITiktokUser } from '@/types/tiktok';

type InitSate = {
  listPages: IAdsAccount[];
  adsAccount: IAdsAccount | undefined;
  tiktokUser: ITiktokUser | undefined;
  isLogin: boolean;
  loading: boolean;
  refetchLoading: boolean;
  updateStateSelected: (id: string) => void;
  logout: () => void;
  handleRefetchListPage: () => void;
  isAccountSelected: boolean;
};

const TiktokAuthContext = createContext<InitSate>({
  listPages: [],
  adsAccount: undefined,
  tiktokUser: undefined,
  isLogin: false,
  loading: false,
  refetchLoading: false,
  updateStateSelected: () => {},
  logout: () => {},
  handleRefetchListPage: () => {},
  isAccountSelected: false,
});

export const TiktokAuthProvider = ({ children }: { children: ReactNode }) => {
  const [listPages, setListPages] = useState<IAdsAccount[]>([]);

  const [isLogin, setIsLogin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [adsAccount, setAdsAccount] = useState<IAdsAccount>();
  const [tiktokUser, setTiktokUser] = useState<ITiktokUser>();
  const [refetchLoading, setRefetchLoading] = useState<boolean>(false);
  const controllerRef = useRef<AbortController | null>(null);
  const [isAccountSelected, setIsAccountSelected] = useState<boolean>(false);

  const setDefaultState = () => {
    setIsLogin(false);
    setListPages([]);
    setAdsAccount(undefined);
    setTiktokUser(undefined);
    setLoading(false);
    setRefetchLoading(false);
    deleteStorage(TIKTOK_STORAGE_KEY);
    controllerRef.current?.abort();
  }

  const mutationListPages = useMutation({
    mutationFn: async () => {
      controllerRef.current?.abort();
      const controller = new AbortController();
      controllerRef.current = controller;
      return await tiktokOauthApi.getTiktokUser({ signal: controller.signal });
    },
    onSuccess: (data) => {
      if (data.code === 1001) {
        setIsLogin(false);
        setListPages([]);
        setLoading(false);
        return;
      }
      setIsLogin(true);
      setListPages(data.data.ad_accounts);
      setAdsAccount({
        ad_account_id: data.data.ad_account_default,
        ad_account_name:
          data.data.ad_accounts.find(
            (item: IAdsAccount) => item.ad_account_id === data.data.ad_account_default,
          )?.ad_account_name || '',
      });
      setTiktokUser(data.data);
      setLoading(false);
      setRefetchLoading(false);
    },
    onError: (e) => {
      setDefaultState();
      return e;
    },
  });

  const logoutMutation = useMutation({
    mutationFn: tiktokOauthApi.disconnect,
    onSuccess: () => {
      setDefaultState();
    },
  });

  useEffect(() => {
    const isSelected = listPages.some((page) => page.ad_account_id === adsAccount?.ad_account_id);
    setIsAccountSelected(isSelected);
    if (adsAccount) {
      setStorage(TIKTOK_STORAGE_KEY, String(true));
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listPages]);

  const logout = () => {
    logoutMutation.mutate();
  };

  const updateStateSelected = (id: string) => {
    setListPages((prev) => {
      return prev.map((item) => {
        return {
          ...item,
          selected: item.ad_account_id === id,
        };
      });
    });

    setStorage(TIKTOK_STORAGE_KEY, String(true));
  };

  const handleRefetchListPage = () => {
    setRefetchLoading(true);
    mutationListPages.mutate();
  };

  useEffect(() => {
    setLoading(true);
    mutationListPages.mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <TiktokAuthContext.Provider
      value={{
        listPages,
        adsAccount,
        tiktokUser,
        isLogin,
        loading,
        refetchLoading,
        logout,
        updateStateSelected,
        handleRefetchListPage,
        isAccountSelected,
      }}
    >
      {children}
    </TiktokAuthContext.Provider>
  );
};

export const useTiktokContext = (): InitSate => useContext(TiktokAuthContext);
