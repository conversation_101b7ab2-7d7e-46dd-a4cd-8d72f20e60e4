import { useEffect, useState, useRef } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/tiktok';
import FilterPanel from '@/pages/TiktokAds/components/FilterPanel';
import audienceCol from '@/pages/TiktokAds/components/Column/audienceCol';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { formatDateYYYYMMDD } from '@/utils/helper';
import { IAudienceDetail } from '@/types/audience';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
  loading?: boolean;
  tiktokCustomAudience: ICustomAudienceResponse;
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const CustomTiktokAudiences = ({
  filterPayload,
  setFilterPayload,
  loading,
  tiktokCustomAudience,
  onOpenHistoryModal,
  onOpenUpdateModal,
}: ICustomAudiences) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const listAudienceTiktok = tiktokCustomAudience.items ?? [];
  const { adsAccount } = useTiktokContext();

  const [idActive, setIdActive] = useState<number>(0);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(listAudienceTiktok);
  const [loadingDetailIds, setLoadingDetailIds] = useState<Set<number>>(new Set());
  const fetchedDetailIds = useRef<Set<number>>(new Set());

  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!!listAudienceTiktok.length) {
      setListAudiences(listAudienceTiktok);
    }
  }, [listAudienceTiktok]);

  useEffect(() => {
    if (idActive > 0 && !loadingDetailIds.has(idActive) && !fetchedDetailIds.current.has(idActive)) {
      const targetItem = listAudiences.find((item) => item.job_id === idActive);

      if (targetItem && (targetItem.total_records === 0 || targetItem.status === 'PENDING')) {
        fetchedDetailIds.current.add(idActive);
        setLoadingDetailIds(prev => new Set(prev).add(idActive));

        get({
          endpoint: ENDPOINTS.custom_audience.detail(idActive.toString()),
        }).then((res) => {
          const dataAudience = res?.data?.data as unknown as IAudienceDetail;
          setListAudiences((prev) =>
            prev.map((item) =>
              item.job_id === dataAudience.job_id? dataAudience : item,
            ),
          );
        }).catch((error) => {
          console.error('Error fetching audience detail:', error);
          fetchedDetailIds.current.delete(idActive);
        }).finally(() => {
          setLoadingDetailIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(idActive);
            return newSet;
          });
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={tiktokCustomAudience.items}
        columns={audienceCol({
          act: adsAccount?.ad_account_id || '',
          setIdActive,
          onOpenHistoryModal,
          onOpenUpdateModal
        })}
        total={tiktokCustomAudience.count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
    </div>
  );
};
export default CustomTiktokAudiences;
