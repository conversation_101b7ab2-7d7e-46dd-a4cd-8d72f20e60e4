export const ENDPOINTS = {
  hotline: '/api/v1/hotline/',
  using_hotline: '/api/v1/hotline/choose/',
  hotline_bought: '/api/v1/hotline/bought/',
  remove_hotline: '/api/v1/hotline/remove-choose/',
  remove_hotline_bought: '/api/v1/hotline/remove/',
  cancelRenewalHotline: (id: string) => `/api/v1/hotline/cancel-renewal/${id}/`,
  updateRenewalHotline: (id: string) => `/api/v1/hotline/update-renewal/${id}/`,
  add_phones: '/api/v1/hotline/add/',
  contact_list: '/api/v1/contact/',
  contact_limit: '/api/v1/auth/contact-limit/',
  report_contact: '/api/v1/contact/report/',
  history_call: (idContact: string) => `/api/v1/contact/${idContact}/history-call/`,
  activity: (idContact: string) => `/api/v1/contact/${idContact}/activity/`,
  contact_list_clean_up: '/api/v1/contact/clean-up/',
  contact_list_restore: '/api/v1/contact/restore/',
  empty_trash_contact: '/api/v1/contact/empty-trash/',
  bulk_delete_contact: '/api/v1/contact/bulk-delete/',
  upload_contact: '/api/v1/contact/import/',
  auth: {
    me: '/api/v1/auth/me/',
    refresh: '/api/v1/auth/refresh/',
  },
  reminder: '/api/v1/reminder/',
  segments: {
    getList: '/api/v1/segment/',
    create: '/api/v1/segment/',
    update: '/api/v1/segment/',
    delete: '/api/v1/segment/',
    detail: '/api/v1/segment/',
    cleanUp: (id: string) => `/api/v1/segment/${id}/clean-up/`,
  },
  notification: {
    get: '/api/v1/notification/',
    read: (id: string) => `/api/v1/notification/${id}/`,
    mark_all_read: '/api/v1/notification/mark-all-read/',
  },
  location: {
    getList: '/api/v1/persona/admin-area/',
  },
  call_center: {
    event_stream: '/event/notifications/stream',
  },
  fb: {
    custom_audience: '/api/v1/facebook-ads/custom-audience/',
    push_custom_audience: '/api/v1/facebook-ads/custom-audience/large-upload/',
    campaign: '/api/v1/facebook-ads/campaign/',
    log: '/api/v1/facebook-ads/custom-audience/jobs',
    log_detail: (id: number) => `/api/v1/facebook-ads/custom-audience/jobs/${id}`,
  },
  fb_Oauth: {
    '': '/api/v1/oauth/facebook/',
    callback: '/api/v1/oauth/facebook/callback/',
    ad_account: '/api/v1/oauth/facebook/ad-accounts/',
    remove_account: '/api/v1/oauth/facebook/ad-accounts/removed/',
    page: '/api/v1/oauth/facebook/pages/',
    logout: '/api/v1/oauth/facebook/log-out/',
  },
  facebook: {
    avatar: (fb_uid: string) => `/api/v1/social-data/fb-img/${fb_uid}/?type=profile`,
  },
  tiktok: {
    '': '/api/v1/tiktok-ads/',
    authLink: '/api/v1/tiktok-ads/auth-link/',
    callback: '/api/v1/tiktok-ads/callback/',
    ad_account: '/api/v1/tiktok-ads/ad-accounts/',
    logout: '/api/v1/tiktok-ads/log-out/',

    largeUpload: '/api/v1/tiktok-ads/custom-audience/large-upload/',
    largeUpdate: '/api/v1/tiktok-ads/custom-audience/large-upload/update',
  },
  custom_audience: {
    '': '/api/v1/custom-audience/jobs',
    detail: (jobId: string) => `/api/v1/custom-audience/jobs/${jobId}`,
    history: (jobId: number) => `/api/v1/custom-audience/jobs/${jobId}/history`,
    countJobs: '/api/v1/custom-audience/segment/count-jobs',
  },
  zalo: {
    '': '/api/v1/oauth/zalo/',
    oa: '/api/v1/oauth/zalo/oa/',
    logout: (oa_id: string) => `api/v1/oauth/zalo/oa/${oa_id}/log-out/`,
    createTemp: (oa_id: string) => `/api/v1/zns/${oa_id}/template/create/`,
    uploadImage: (oa_id: string) => `/api/v1/zns/${oa_id}/upload/image/`,
    getList: (oa_id: string) => `/api/v1/zns/${oa_id}/template/`,
    createCampaign: (oa_id: string) => `/api/v1/zns/${oa_id}/capaign/`,
    getListCampaign: (oa_id: string) => `/api/v1/zns/${oa_id}/campaign/`,
    getTempDetail: ({ oa_id, temp_id }: { oa_id: string; temp_id: string }) =>
      `/api/v1/zns/${oa_id}/template/${temp_id}/`,
    updateTempDetail: (oa_id: string) => `/api/v1/zns/${oa_id}/template/update/`,
    deleteTemp: (oa_id: string, template_id: string) =>
      `/api/v1/zns/${oa_id}/template/${template_id}/delete/`,
  },
  note: {
    create: (contact_id: string) => `/api/v1/contact/${contact_id}/note/`,
    update: (contact_id: string, id: string) => `/api/v1/contact/${contact_id}/note/${id}/`,
  },
  report: {
    get: '/api/v1/contact/report/',
  },
  email: {
    createSender: '/api/v1/emails/senders/',
    verifySender: (sender_id: string) => `/api/v1/emails/senders/${sender_id}/verifications/`,
    getOtpSender: '/api/v1/emails/senders/',
    getSenderDetail: (sender_id: string) => `/api/v1/emails/senders/${sender_id}/`,
    deleteSender: (sender_id: string) => `/api/v1/emails/senders/${sender_id}/`,
    updateSender: (sender_id: string) => `/api/v1/emails/senders/${sender_id}/`,
  },
};
