export interface TAudience {
  id: number;
  audience_name: string;
  audience_id: string;
  number_contact: number;
  segment: {
    id: number;
    name: string;
    color: string;
  };
  date_created: string;
}

export interface TAudienceResponse {
  count: number;
  items: TAudience[];
}

export interface TCampaignResponse {
  count: number;
  data: TCampaign[];
}

export type DISTRIBUTION_STATUS = 'ACTIVE' | 'PAUSED' | 'DELETED';

export interface TCampaign {
  id: string;
  name: string; // -> chiến dịch
  status: DISTRIBUTION_STATUS; // -> phân phối
  objective: 'REACH' | 'MESSAGES'; // -> chiến lược thầu
  start_time: string; // -> thời gian bắt đầu
  stop_time: string | null; // -> thời gian kết thúc
  daily_budget: string | null; // -> ngân sách
  impressions: number; // -> l<PERSON><PERSON><PERSON> hiển thị
  clicks: number; // -> l<PERSON><PERSON><PERSON> ti<PERSON><PERSON> cận
  spend: number; // -> số tiền đã chi
  ctr: number; // Click-through rate -> xếp hạng tương tác
  cpm: number; // Cost per mille -> chi phí trên mỗi kết quả
  cpc: number; // Cost per click
}

export interface ISegmentInfo {
  color: string;
  name: string;
}

export interface IAudienceDetail {
  audience_id: string;
  audience_name: string;
  completed_at: string;
  created_at: string;
  error_message: string;
  job_id: number;
  processed_records: number;
  progress: number;
  retry_count: number;
  segment_id: number;
  segment_name: string;
  status: 'COMPLETED' | 'FAILED' | 'PENDING' | 'PROCESSING' | 'COMPLETED_WITH_ERRORS';
  total_records: number;
  workflow_id: string;
  is_initial: boolean;
  updated_at: string;
  segment_info?: ISegmentInfo;
}

export interface ICountJobs {
  count: number;
}
