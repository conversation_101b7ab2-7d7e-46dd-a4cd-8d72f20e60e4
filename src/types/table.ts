import { Column, ColumnDef, Row, SortingState, Table } from '@tanstack/react-table';
import React, { RefObject } from 'react';
import {
  DefaultError,
  FetchNextPageOptions,
  InfiniteQueryObserverResult,
} from '@tanstack/react-query';

export type TParams = {
  page: number;
  limit: number;
};
export type TFilter = {
  isSearch: boolean;
  filterNode?: React.ReactNode;
  leftNodeFilter?: React.ReactNode;
  centerNodeFilter?: React.ReactNode;
  rightNodeFilter?: React.ReactNode;
  bottomNodeFilter?: React.ReactNode;
  isFilter: boolean;
};
export type TPagination = {
  currentPage: number;
  pageSize: number;
};

export interface TPaginationSearch extends TPagination {
  search?: string;
}

export type TNewCol<T> = Column<T> & {
  columnDef: TNewColumnDef<T>;
};

type TNewColumnDef<T> = ColumnDef<T> & {
  pin?: 'left' | 'right';
  accessorKey?: string;
  enableSorting?: boolean;
  isResizingColumn?: boolean;
};

export type TTableProps<T extends object> = {
  data: T[];
  columns: ColumnDef<T>[];
  isToolbar?: boolean;
  isPerPageChange?: boolean;
  searchColumn?: string;
  searchPlaceholder?: string;
  loading?: boolean;
  total: number;
  renderSubComponent?: (row: T) => React.ReactNode;
  getRowCanExpand?: (row: Row<T>) => boolean;
  isSelectCol?: boolean;
  body?: (data: Table<T>) => React.ReactNode;
  getRowsSelected?: (row: T[]) => void;
  getSortingChange?: (sort: SortingState) => void;
  onResetFilter?: (ref: () => void) => void;
  filter?: TFilter;
  className?: string;
  classNameHeader?: string;
  classNameBody?: string;
  containerClassName?: string;
  pagination?: TPagination;
  setPagination?: (pagination: TPagination) => void;
  isShowFilter?: boolean;
  searchPlaceHolder?: string;
  popoverStates?: Record<string, boolean>;
  setPopoverStates?: (states: Record<string, boolean> | ((prev: Record<string, boolean>) => Record<string, boolean>)) => void;
  refreshingRows?: Set<string>;
  setRefreshingRows?: (rows: Set<string> | ((prev: Set<string>) => Set<string>)) => void;
  scrollRef?: RefObject<HTMLDivElement>;
  isSingle?: boolean;
  notfound?: React.ReactNode;
};
export type TBodyTableProps<T extends object> = {
  table: Table<T>;
  data: T[];
  columns: ColumnDef<T>[];
  totalCount?: number;
  totalFetch?: number;
  fetchNextPage?: (
    options?: FetchNextPageOptions,
  ) => Promise<InfiniteQueryObserverResult<unknown, DefaultError>>;
  isFetching?: boolean;
  isLoading?: boolean;
  className?: string;
  scrollContainerRef?: RefObject<HTMLDivElement>;
  syncNoData?: boolean
};
