import { get, update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { FormDatePicker } from '@/components/Form/FormDatePicker';
import { FormInput } from '@/components/Form/FormInput';
import { FormSelect } from '@/components/Form/FormSelect';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import {
  IOptions,
  TContactCreate,
  TContactItems,
  TContactListResponse,
  TContactRestore,
} from '@/types/contactList';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { genderSelectOptions } from '@/utils/constants';
import { checkObject, formatDateYYYYMMDD } from '@/utils/helper';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { Submit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { formSchemaContactDetail } from '@/types/validation';
import { DialogClose } from '@/components/ui/dialog';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { FormArea } from '@/components/Form/FormArea';
import { contactApi } from '@/apis/contactApi';
import { RiInformationLine, RiLoader2Line } from '@remixicon/react';
import { useNavigate } from 'react-router-dom';
import CustomToolTips from '@/components/CustomToolTips';
import { SelectSearch } from '@/components/SelectSearch';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { TSegment } from '@/types/segment';
import { handleGetContactLimit } from '@/store/ContactLimit/action';

type TModalCreateContact = {
  isCreate: boolean;
  detailContact?: TContactItems;
  handleSubmitSuccess: () => void;
};

type TContactFound = {
  id: number;
  phone: string;
};

type TError = { response: { data: { error: string } } };

const n = (key: keyof TContactCreate) => key;

export const ContactSubmitForm: React.FC<TModalCreateContact> = ({
  ...props
}: TModalCreateContact) => {
  const { isCreate, detailContact, handleSubmitSuccess } = props;
  const { t } = useTranslation();
  const { allDatasetSegments, allSegments } = useAppSelector(getSelector('segment'));
  const { locationData } = useAppSelector(getSelector('location_province'));
  const dispatch = useAppDispatch();
  const refClickCloseModal = useRef<HTMLButtonElement>(null);
  const navigate = useNavigate();
  const [notiDuplicate, setNotiDuplicate] = useState<string>(t('common.duplicatePhoneNumber'));
  const [disable, setDisable] = useState<boolean>(true);
  const [contactFound, setContactFound] = useState<TContactFound>({ id: 0, phone: '' });
  const [contact, setContact] = useState<TContactItems>();
  const {
    control,
    reset,
    handleSubmit,
    setError,
    watch,
    formState: { isDirty },
  } = useForm<TContactCreate>({
    resolver: zodResolver(formSchemaContactDetail),
    defaultValues: {},
  });

  useEffect(() => {
    if (detailContact) {
      setContact(detailContact);
    } else {
      setDisable(false);
    }
  }, [detailContact]);

  const handleSetForm = () => {
    if (!detailContact) {
      return;
    }
    const updateValues: TContactCreate = Object.keys(detailContact).reduce((acc, key) => {
      const k = key as keyof TContactItems;
      if (k !== 'id') {
        (acc[k] as unknown) = (detailContact[k] ?? '') as unknown;
      }
      return acc;
    }, {} as TContactCreate);
    reset({
      ...updateValues,
      segment_ids: updateValues.segments?.map((item) => item.segment.id.toString()),
      note_content: detailContact.note?.note ?? '',
    });
  };

  useEffect(() => {
    if (!isCreate) {
      handleSetForm();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCreate]);

  const handleSubmitSuccessForm = (error: unknown) => {
    if (error === null) {
      handleSubmitSuccess();
      toast({
        status: 'success',
        description: t(
          isCreate ? 'contactList.contactCreateSuccess' : 'contactList.contactUpdateSuccess',
        ),
      });
      dispatch(handleGetContactLimit());
      refClickCloseModal.current?.click();
    }
  };

  const createContactMutation = useMutation({
    mutationFn: async (payload: TContactCreate): Promise<TBaseResponse<TContactCreate>> => {
      return contactApi
        .createContact({
          endpoint: ENDPOINTS.contact_list,
          params: payload,
        })
        .then((res) => res.data as TBaseResponse<TContactCreate>);
    },
    onSuccess: (res) => handleSubmitSuccessForm(res.error),
    onError: async (error: TError) => {
      await handleCheckError(error);
    },
  });

  const editContactMutation = useMutation({
    mutationFn: async (payload: TContactCreate): Promise<TBaseResponse<TContactCreate>> => {
      return contactApi
        .updateContact({
          endpoint: ENDPOINTS.contact_list + detailContact?.id + '/',
          params: payload,
        })
        .then((res) => res.data as TBaseResponse<TContactCreate>);
    },
    onSuccess: (res) => handleSubmitSuccessForm(res?.error),
    onError: async (error: TError) => {
      await handleCheckError(error);
    },
  });

  const handleCheckError = async (error: TError) => {
    if (error?.response?.data?.error.includes('phone number')) {
      const phone: string = watch(n('phone_number'))?.toString() ?? '';
      await searchContact({ phone }).then(async (res) => {
        if (!res) {
          await searchContact({ phone, isArchive: true });
        }
      });
    }
  };

  const onSubmit: SubmitHandler<TContactCreate> = (values) => {
    const segment_ids: number[] = contact?.segments.map((item) => item.segment.id) ?? [];
    const dataContact = checkObject({
      ...values,
      dob: formatDateYYYYMMDD(values?.dob ?? '', '-'),
      segment_ids,
    });
    const contactData = { ...dataContact, note_content: values.note_content?.trim() };
    if (isCreate) {
      createContactMutation.mutate(contactData);
    } else {
      editContactMutation.mutate(contactData);
    }
  };

  const onError: SubmitErrorHandler<TContactCreate> = (error) => {
    if (error.phone_number && !watch(n('phone_number'))) {
      setError(n('phone_number'), {
        type: 'custom',
        message: t('common.error.enterYourNumber'),
      });
    }
    if (error.full_name && !watch(n('full_name'))) {
      setError(n('full_name'), { type: 'custom', message: t('common.error.enterYourName') });
    }
  };

  const restoreContactMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.contact_list_restore,
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: () => {
      navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact.detail}/${contactFound.id}`);
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const searchContact = async (data: { phone: string; isArchive?: boolean }) => {
    return await get({
      endpoint: ENDPOINTS.contact_list,
      params: {
        page: 1,
        limit: 1,
        datatype: 'AUDIENCE',
        search: data.phone,
        is_deleted: data.isArchive,
      },
    }).then((res) => {
      const response = res as unknown as TBaseResponse<TContactListResponse>;
      const isValid = response.data.data.items.length;
      if (isValid) {
        const contact = response.data.data.items[0];
        setContactFound({ id: contact.id, phone: contact.phone_number });
        if (data.isArchive) {
          setNotiDuplicate(t('common.restoreArchivePhone'));
        } else {
          setNotiDuplicate(t('common.duplicatePhoneNumber'));
        }
      }
      return isValid;
    });
  };

  const handleSelectSegment = (value: IOptions[]) => {
    setContact(
      (prev) =>
        ({
          ...prev,
          segments: value.map((item) => ({
            segment: { id: parseInt(item.value), name: item.label, color: item.color },
          })),
        }) as TContactItems,
    );
    handleCheckSegmentIds(
      value.map((item) => item.value),
      detailContact?.segments.map((item) => item.segment.id.toString()) ?? [],
    );
  };

  const handleCheckSegmentIds = (ids_select: string[], ids_detail: string[]) => {
    if (ids_select.length !== ids_detail.length) {
      setDisable(false);
      return;
    }
    const sortedArrSelect = [...ids_select].sort();
    const sortedArrDetail = [...ids_detail].sort();
    const isCheck = sortedArrSelect.some((id, index) => id !== sortedArrDetail[index]);
    setDisable(!isCheck);
    return isCheck;
  };

  const handleRestoreContact = () => {
    restoreContactMutation.mutate({
      contact_ids: [contactFound.id],
    });
  };

  const isDataset = detailContact?.datatype === 'DATASET';

  const handleConvertSegment = (segmentList: TSegment[]) => {
    return segmentList.map((item) => {
      return {
        value: item.id.toString(),
        label: item.name,
        color: item.color,
      };
    });
  };

  return (
    <div className="flex flex-col gap-6 w-[802px]">
      <form onSubmit={handleSubmit(onSubmit, onError)}>
        <div className="flex gap-3">
          <div className="flex-1">
            <FormInput
              control={control}
              name={n('full_name')}
              label={
                <div className="flex gap-1 h-[18px] items-end">
                  {t('common.fullName')}
                  <span className="text-delete">*</span>
                </div>
              }
              placeholder={t('placeHolder.contactName')}
            />
            {!isDataset && (
              <FormInput
                control={control}
                name={n('phone_number')}
                className={`${!!contactFound.id ? 'text-delete' : ''}`}
                label={
                  <div className="flex gap-1 h-[18px] items-end">
                    {t('common.phoneNumber')}
                    <span className="text-delete">*</span>
                    {!!contactFound.id && (
                      <span className="text-xs text-delete">
                        <CustomToolTips
                          element={
                            <Button
                              type={'button'}
                              variant={'ghost'}
                              className="p-0 m-0 w-fit h-fit"
                              onClick={handleRestoreContact}
                            >
                              <RiInformationLine size={16} />
                            </Button>
                          }
                          className="z-50"
                          content={notiDuplicate}
                        />
                      </span>
                    )}
                  </div>
                }
                placeholder={t('placeHolder.phoneNumber')}
                type={'tel'}
                onChange={() => {
                  setContactFound({ id: 0, phone: '' });
                }}
              />
            )}

            <FormInput
              control={control}
              name={n('company_name')}
              label={t('common.company')}
              placeholder={t('placeHolder.company')}
            />
            <FormInput
              control={control}
              name={n('person_address')}
              label={t('common.address')}
              placeholder={t('placeHolder.address')}
            />
          </div>
          <div className="flex-1">
            <div className="flex gap-3">
              <FormDatePicker
                control={control}
                name={n('dob')}
                placeholder={t('common.dob')}
                label={''}
              />
              <FormSelect
                control={control}
                name={n('gender')}
                label={''}
                className={'w-[161px]'}
                placeholder={t('placeHolder.gender')}
                listSelect={genderSelectOptions}
              />
            </div>
            <FormInput
              control={control}
              name={n('email')}
              label={t('common.email')}
              placeholder={t('placeHolder.email')}
            />
            <FormInput
              control={control}
              name={n('position')}
              label={t('common.position')}
              placeholder={t('placeHolder.position')}
            />
            {!isDataset && (
              <FormSelect
                control={control}
                name={n('person_province')}
                label={t('common.cityOrProvince')}
                placeholder={t('placeHolder.cityOrProvince')}
                listSelect={locationData}
              />
            )}
          </div>
        </div>
        {isDataset && (
          <FormSelect
            control={control}
            name={n('person_province')}
            label={t('common.cityOrProvince')}
            placeholder={t('placeHolder.cityOrProvince')}
            listSelect={locationData}
          />
        )}
        <div className="mb-6">
          <div className="h-[18px] font-normal text-xs text-secondary mb-1">
            {t('common.segment.title')}
          </div>
          <SelectSearch
            options={handleConvertSegment(isDataset ? allDatasetSegments.items : allSegments.items)}
            defaultValues={contact?.segments.map((item) => ({
              value: item.segment.id.toString(),
              label: item.segment.name,
              color: item.segment.color,
            }))}
            onSelect={handleSelectSegment}
            className="w-full bg-transparent border-input"
            placeholderInput={t('placeHolder.segment')}
            placeholder={t('placeHolder.selectSegment')}
          />
        </div>
        <FormArea
          control={control}
          label={t('common.note')}
          name={n('note_content')}
          className="resize-none"
        />
        <div className="flex gap-4 mt-6">
          <DialogClose className="flex-1" ref={refClickCloseModal}>
            <div className="flex items-center justify-center h-[40px] font-medium w-full bg-secondary rounded-lg text-sm text-primary hover:bg-secondary-foreground_crm hover:text-filter">
              {t('common.button.cancel')}
            </div>
          </DialogClose>
          <Button
            type="submit"
            variant={'primary'}
            disabled={
              editContactMutation.isPending ||
              createContactMutation.isPending ||
              (!isDirty && disable)
            }
            className="flex-1 h-[40px] w-full"
          >
            {createContactMutation.isPending ? (
              <RiLoader2Line className="mx-auto animate-spin" />
            ) : (
              t('common.button.saveChange')
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
