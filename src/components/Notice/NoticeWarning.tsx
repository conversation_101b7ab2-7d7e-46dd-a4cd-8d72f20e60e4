import React from 'react';
import { Box } from '@/components/Box';
import { RiInformation2Fill } from '@remixicon/react';

interface INoticeWarning {
  children: React.ReactNode;
}

export const NoticeWarning: React.FC<INoticeWarning> = ({ children }: INoticeWarning) => {
  return (
    <Box className="w-full gap-2 items-start justify-start bg-big360Color-warning-50 border border-big360Color-warning-100 p-3 rounded-xl">
      <RiInformation2Fill color={'#BB8700'} size={20} />
      <div className="text-sm font-semibold text-big360Color-warning-700">{children}</div>
    </Box>
  );
};
