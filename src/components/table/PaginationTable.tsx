import type React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import PageRow from './PageRow';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import usePageNumber from '@/hooks/usePageNumber';

type Props = {
  page: number;
  setPage: (page: number) => void;
  totalPages: number;
  limit: number;
  countData?: number;
  setLimit: (limit: number) => void;
  isChangePerPage?: boolean;
};

const PaginationTable = ({
  page,
  setPage,
  totalPages,
  limit,
  setLimit,
  isChangePerPage = true,
}: Props) => {
  const { t } = useTranslation();

  const handlePrevious = (e: React.MouseEvent) => {
    e.preventDefault();
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNext = (e: React.MouseEvent) => {
    e.preventDefault();
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  const { getPageNumbers } = usePageNumber({
    page,
    totalPages,
  });

  return (
    <Pagination className="mt-3 mr-auto justify-between items-center flex gap-1 w-full">
      {isChangePerPage && (
        <div className="flex gap-2 items-center font-medium text-secondary text-sm">
          <p className='text-sm text-big360Color-neutral-500'>{t('common.rowPage')}</p>
          <PageRow page={limit} setPage={(value) => setLimit(value)} />
        </div>
      )}
      <PaginationContent className="w-fit">
        <PaginationItem>
          <PaginationPrevious
            onClick={handlePrevious}
            className={cn('cursor-pointer', page <= 1 ? 'pointer-events-none opacity-50' : '')}
          />
        </PaginationItem>

        {getPageNumbers.map((pageNumber, index) =>
          pageNumber === 'ellipsis' ? (
            <PaginationItem key={`ellipsis-${index}`}>
              <PaginationEllipsis />
            </PaginationItem>
          ) : (
            <PaginationItem key={pageNumber}>
              <PaginationLink
                className={cn(
                  'cursor-pointer border-none shadow-none',
                  page === pageNumber ? 'text-primary font-medium' : 'text-muted-foreground',
                )}
                isActive={page === pageNumber}
                onClick={(e) => {
                  e.preventDefault();
                  setPage(pageNumber);
                }}
              >
                <p>{Number(pageNumber).toLocaleString()}</p>
              </PaginationLink>
            </PaginationItem>
          ),
        )}

        <PaginationItem>
          <PaginationNext
            onClick={handleNext}
            className={cn(page >= totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer')}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

export default PaginationTable;
