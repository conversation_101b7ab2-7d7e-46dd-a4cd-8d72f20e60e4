import React, { ComponentPropsWithoutRef } from 'react';
import { FormItem } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { RiArrowDownSLine, RiCalendarLine } from '@remixicon/react';
import {
  Controller,
  ControllerProps,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import { ErrorField } from '@/components/Form/ErrorField';
import { formatDate } from '@/utils/helper';

type TFormDatePickerPropsExtend = {
  name: string;
  label?: React.ReactNode;
  placeholder?: string;
  onChange?: (value: Date | undefined) => void;
  className?: string;
  isRemoveLabel?: boolean;
  isRemoveDisabledDate?: boolean;
  disableDate?: (date: Date) => boolean;
};

type TFormDatePickerProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Pick<ControllerProps<TFieldValues, TName>, 'name' | 'control' | 'rules'> &
  ComponentPropsWithoutRef<'select'> &
  TFormDatePickerPropsExtend;

export function FormDatePicker<
  T extends FieldValues = FieldValues,
  U extends FieldPath<T> = FieldPath<T>,
>({ ...props }: TFormDatePickerProps<T, U>) {
  const {
    name,
    control,
    rules,
    onChange,
    className,
    label,
    placeholder,
    isRemoveLabel,
    isRemoveDisabledDate,
    disableDate,
  } = props;
  const handleChange = (
    field: ControllerRenderProps<T, U> | ControllerRenderProps<FieldValues, string>,
    e: Date | undefined,
  ) => {
    field.onChange(e);
    if (onChange && e) {
      onChange(e);
    }
  };
  return (
    <FormItem className="mb-6">
      {isRemoveLabel ? (
        <></>
      ) : (
        <div className={cn('h-[18px]', label ? 'font-normal text-xs text-secondary' : '')}>
          {label}
        </div>
      )}
      <Controller
        name={name}
        rules={rules}
        control={control}
        render={({ field, fieldState }) => {
          const isError = !!fieldState?.error?.message;
          return (
            <div className="relative">
              <Popover>
                <PopoverTrigger asChild className='data-[state=open]:border-big360Color-brand-500 border-input'>
                  <Button
                    disabled={props.disabled}
                    variant={'outline'}
                    className={cn(
                      'w-[222px] pl-3 text-left font-normal h-[40px] mt-1 rounded-xl bg-background-foreground text-sm text-filter hover:text-filter hover:bg-transparent justify-between',
                      !field.value && 'text-muted-foreground',
                      props.disabled && 'disabled:cursor-not-allowed disabled:pointer-events-auto',
                      isError ? 'border-delete outline-none' : '',
                      className,
                    )}
                  >
                    <div className={cn('flex gap-2 items-center text-filter', field.value && 'text-primary')}>
                      <RiCalendarLine size={16} />
                      {field.value ? formatDate(field.value, '/') : <span>{placeholder}</span>}
                    </div>
                    <RiArrowDownSLine size={16} className="text-filter" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 rounded-xl border-tertiary" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value ? new Date(field.value) : undefined}
                    defaultMonth={field.value ? new Date(field.value) : new Date()}
                    showOutsideDays={true}
                    onSelect={(e) => {
                      if (!!e) {
                        handleChange(field, e);
                      }
                    }}
                    disabled={
                      isRemoveDisabledDate
                        ? undefined
                        : disableDate
                          ? disableDate
                          : (date) => date > new Date() || date < new Date('1900-01-01')
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {fieldState.error && fieldState.error.message && (
                <ErrorField message={fieldState.error.message} />
              )}
            </div>
          );
        }}
      />
    </FormItem>
  );
}
