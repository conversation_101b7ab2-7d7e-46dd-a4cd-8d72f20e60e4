import * as React from 'react';
import { RefObject, useEffect, useRef, useState } from 'react';
import { cn, removeVietnameseTones } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RiArrowDownSLine, RiCloseCircleFill } from '@remixicon/react';
import SearchBar from './SearchBar';
import ShowMore from '@/components/ShowMore';
import { useTranslation } from 'react-i18next';
import { IOptions, Option } from '@/types/contactList';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TagFillIcon } from '@/assets/TagFillIcon';

interface MultipleSelectProps {
  options: Option[];
  selected: string[];
  title?: string;
  onChange: (selected: string[]) => void;
  placeholder?: string;
  maxItems?: number;
  disabled?: boolean;
  icon?: React.ReactNode;
  className?: string;
  isHideSearch?: boolean;
  extraButton?: React.ReactNode;
}

// const MORE_ITEMS = 2;

export function MultipleSelect({
  options,
  selected,
  title,
  onChange,
  placeholder = 'Select items...',
  maxItems,
  disabled = false,
  className = '',
  icon,
  isHideSearch = false,
  extraButton,
}: MultipleSelectProps) {
  const { t } = useTranslation();
  const [open, setOpen] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [width, setWidth] = useState<number>(0);
  const triggerRef: RefObject<HTMLButtonElement> = useRef(null);

  const handleSelect = (value: string) => {
    if (maxItems && selected.length >= maxItems && !selected.includes(value)) {
      return;
    }
    const updatedSelected = selected.includes(value)
      ? selected.filter((item) => item !== value)
      : [...selected, value];
    onChange(updatedSelected);
  };

  useEffect(() => {
    document.body.style.pointerEvents = '';
  }, [open]);

  const handleRemove = (valueToRemove: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange(selected.filter((value) => value !== valueToRemove));
  };

  const handleClearAll = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange([]);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !open) {
      e.preventDefault();
      setOpen(true);
    }
  };

  const filteredOptions = options.filter((option) =>
    removeVietnameseTones(option.label.toLowerCase()).includes(
      removeVietnameseTones(searchQuery.toLowerCase()),
    ),
  );
  // const showPlaceholderTop = selected.length > 0;
  const filterSelectedOptions = (data: IOptions[], selectedValues: string[]) => {
    return data.filter((option) => selectedValues.includes(option.value.toString()));
  };

  useEffect(() => {
    if (triggerRef.current) {
      const updateWidth = () => {
        const newWidth: number = triggerRef?.current?.offsetWidth ?? 0;
        setWidth(newWidth);
      };

      updateWidth();

      window.addEventListener('resize', updateWidth);
      window.addEventListener('load', updateWidth);
      return () => {
        window.removeEventListener('resize', updateWidth);
        window.removeEventListener('load', updateWidth);
      };
    }
  }, []);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild ref={triggerRef}>
        <button
          className={cn(
            'min-w-[140px] h-10 cursor-pointer relative flex items-center justify-center gap-1 px-2 border-[1px] border-tertiary data-[state=open]:border-big360Color-brand-500 rounded-xl',
            className,
            disabled && 'opacity-50 pointer-events-none',
          )}
          onClick={() => !disabled && setOpen(!open)}
          onKeyDown={handleKeyDown}
        >
          <span
            className={cn(
              'absolute left-2 flex gap-1 items-center px-1 bg-white text-sm transition-all duration-300 pointer-events-none text-filter',
              // showPlaceholderTop ? '-top-3 text-xs' : 'top-2',
              'top-2',
            )}
          >
            <span>{icon}</span>
            <span>{placeholder}</span>
          </span>
          <div className="flex justify-end items-center w-[fit] ml-auto bg-white z-10">
            <div className="flex flex-wrap gap-1 items-center">
              {/*{selected.slice(0, MORE_ITEMS).map((value) => {*/}
              {/*  const valueChecked = value.toString();*/}
              {/*  const option = options.find((opt) => opt.value.toString() === valueChecked);*/}
              {/*  return (*/}
              {/*    option && (*/}
              {/*      <SelectItem*/}
              {/*        key={valueChecked}*/}
              {/*        title={option.label}*/}
              {/*        onClick={(value, e) => handleRemove(value, e)}*/}
              {/*        value={valueChecked}*/}
              {/*      />*/}
              {/*    )*/}
              {/*  );*/}
              {/*})}*/}
              {selected.length > 0 && (
                <>
                  <ShowMore
                    onRemove={handleRemove}
                    selected={filterSelectedOptions(options, selected.slice(0))}
                  >
                    <div className="cursor-pointer" onClick={handleClearAll}>
                      <RiCloseCircleFill className="ml-1" size={14} color="#0D112666" />
                    </div>
                  </ShowMore>
                </>
              )}
            </div>
          </div>
          {selected.length === 0 && (
            <RiArrowDownSLine size={20} className="ml-2 shrink-0 opacity-50" />
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent
        isShowArrow={false}
        align={'start'}
        className="p-0 border-tertiary rounded-2xl shadow-filter"
        style={{ width }}
      >
        <div className="rounded-2xl top-0 sticky bg-card p-2">
          {title && (
            <div className="bg-secondary-foreground_crm p-3 rounded-xl">
              <p className="text-primary font-medium text-sm">{title}</p>
            </div>
          )}
          {!isHideSearch && (
            <SearchBar
              className="h-11 p-2 mt-2"
              iconSize={16}
              value={searchQuery}
              placeholder={t('common.search')}
              setSearchQuery={setSearchQuery}
            />
          )}
        </div>
        <ScrollArea className="px-2 pb-0 mb-2" classViewPort="max-h-[318px]">
          {filteredOptions.map((option) => {
            const valueChecked = option.value.toString();
            const checked = selected.includes(valueChecked);
            return (
              <div
                key={valueChecked}
                className={cn(
                  'flex items-center capitalize text-sm gap-1 p-2.5 rounded-xl flex-1 mb-2 cursor-pointer h-[36px]',
                  checked
                    ? 'bg-hover-foreground text-hover-filter font-medium'
                    : 'hover:bg-secondary-foreground_crm font-medium',
                )}
                style={{
                  width: `${width - 20}px`,
                }}
                onClick={() => handleSelect(valueChecked)}
              >
                {option.color && <TagFillIcon color={option.color} />}
                <p className="truncate">{option.label}</p>
              </div>
            );
          })}
        </ScrollArea>
        {extraButton}
      </PopoverContent>
    </Popover>
  );
}
