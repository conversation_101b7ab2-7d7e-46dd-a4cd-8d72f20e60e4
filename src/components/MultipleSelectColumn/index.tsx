import React, { RefObject, useEffect, useRef, useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IOptionsFilter } from '@/types/contactList';
import { RiArrowDownSLine, RiCloseCircleFill } from '@remixicon/react';
// import SelectItem from '@/components/SelectItem';
import ShowMore from '@/components/ShowMore';
import { cn } from '@/lib/utils';

// const MORE_ITEMS = 2;

interface IMultipleSelectColumn {
  data: IOptionsFilter[];
  title: string;
  icon: React.ReactNode;
  selected: string[];
  onChange: (selected: string[]) => void;
  className?: string;
  extraButton?: React.ReactNode;
}

const MultipleSelectColumn: React.FC<IMultipleSelectColumn> = ({
  ...props
}: IMultipleSelectColumn) => {
  const { data, title, icon, selected, className, extraButton, onChange } = props;
  const [width, setWidth] = useState<number>(0);
  const triggerRef: RefObject<HTMLButtonElement> = useRef(null);
  const handleRemove = (valueToRemove: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange(selected.filter((value) => value !== valueToRemove));
  };

  const handleClearAll = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange([]);
  };

  const handleSelect = (value: string) => {
    const updatedSelected = selected.includes(value)
      ? selected.filter((item) => item !== value)
      : [...selected, value];
    onChange(updatedSelected);
  };

  const filterSelectedOptions = (data: IOptionsFilter[], selectedValues: string[]) => {
    return data.flatMap((group) =>
      group.options.filter((option) => selectedValues.includes(option.value)),
    );
  };

  useEffect(() => {
    if (triggerRef.current) {
      const updateWidth = () => {
        const newWidth: number = triggerRef?.current?.offsetWidth ?? 0;
        setWidth(newWidth);
      };

      updateWidth();

      window.addEventListener('resize', updateWidth);
      window.addEventListener('load', updateWidth);
      return () => {
        window.removeEventListener('resize', updateWidth);
        window.removeEventListener('load', updateWidth);
      };
    }
  }, []);

  // const showPlaceholderTop = selected.length > 0;
  return (
    <Popover>
      <PopoverTrigger asChild ref={triggerRef}>
        <button
          className={cn(
            'h-10 cursor-pointer relative flex items-center justify-center gap-1 px-2 border-[1px] border-tertiary data-[state=open]:border-big360Color-brand-500 rounded-xl',
            className,
          )}
        >
          <span
            className={cn(
              'absolute left-2 flex gap-1 items-center px-1 bg-white text-sm transition-all duration-300 text-filter',
              // showPlaceholderTop ? '-top-3 text-xs' : 'top-2',
              'top-2',
            )}
          >
            <span>{icon}</span>
            <span>{title}</span>
          </span>
          <div className="flex justify-end items-center w-[fit] ml-auto bg-white z-10">
            <div className="flex gap-1 items-center">
              {/*{selected.slice(0, MORE_ITEMS).map((value) => {*/}
              {/*  const optionData = data*/}
              {/*    .flatMap((opt) => opt.options)*/}
              {/*    .find((item) => item.value === value);*/}

              {/*  return (*/}
              {/*    <SelectItem*/}
              {/*      key={value}*/}
              {/*      title={optionData?.label ?? ''}*/}
              {/*      onClick={(_, e) => handleRemove(optionData?.value ?? '', e)}*/}
              {/*      value={optionData?.value ?? ''}*/}
              {/*    />*/}
              {/*  );*/}
              {/*})}*/}
              {selected.length > 0 && (
                <ShowMore
                  onRemove={handleRemove}
                  selected={filterSelectedOptions(data, selected.slice(0))}
                >
                  <div onClick={handleClearAll}>
                    <RiCloseCircleFill className="ml-1" size={14} color="#0D112666" />
                  </div>
                </ShowMore>
              )}
            </div>
          </div>
          {selected.length === 0 && (
            <RiArrowDownSLine size={20} className="ml-2 shrink-0 opacity-50" />
          )}{' '}
        </button>
      </PopoverTrigger>
      <PopoverContent
        isShowArrow={false}
        align={'start'}
        className="px-2 py-3 border-[1px] border-tertiary rounded-2xl shadow-filter"
        style={{ width }}
      >
        <div className="flex">
          {Array.isArray(data) &&
            data.map((option, index) => {
              return (
                <div key={'option' + index} className="flex-1 w-[100px]">
                  <div
                    className={`bg-secondary-foreground_crm p-3 mb-2 ${
                      index === 0 ? 'rounded-l-xl' : ''
                    } ${data.length - 1 === index ? 'rounded-r-xl' : ''}`}
                  >
                    <p className="text-primary font-medium text-sm">{option.title}</p>
                  </div>
                  {Array.isArray(option.options) && option.options.length > 0 && (
                    <ScrollArea className="h-[308px]">
                      {option.options.map((optionData) => {
                        const checked = selected.includes(optionData.value);
                        return (
                          <div
                            key={optionData.value}
                            className={cn(
                              'flex cursor-pointer items-center capitalize text-sm gap-1 p-2.5 rounded-xl flex-1 mb-2 w-11/12 h-[36px]',
                              checked
                                ? 'bg-hover-foreground text-hover-filter font-medium'
                                : 'hover:bg-secondary-foreground_crm font-medium',
                            )}
                            onClick={() => handleSelect(optionData.value)}
                          >
                            <p className="truncate">{optionData.label}</p>
                          </div>
                        );
                      })}
                    </ScrollArea>
                  )}
                  {extraButton}
                </div>
              );
            })}
        </div>
      </PopoverContent>
    </Popover>
  );
};
export default MultipleSelectColumn;
