import Modal from '@/components/Modal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { IconBackground } from '@/components/IconBackground';
import { RiErrorWarningLine } from '@remixicon/react';

interface ILimitUpdateAudience {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const LimitUpdateAudience: React.FC<ILimitUpdateAudience> = (
  props: ILimitUpdateAudience,
) => {
  const { open, setOpen } = props;
  const { t } = useTranslation();

  return (
    <Modal
      openModal={open}
      onCloseModal={setOpen}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="max-w-[560px]"
      titleAlign={'center'}
      title={<IconBackground type={'error'} icon={<RiErrorWarningLine size={32} />} />}
    >
      <div className="flex flex-col items-center gap-4">
        <div className="text-center">
          <p className="font-medium text-lg mb-1">{t('tiktokAds.updateFrequency')}</p>
          <p className="text-center text-sm text-secondary max-w-md">
            {t('audience.updateLimitMessage')}
          </p>
        </div>
        <Button onClick={() => setOpen(false)} className="px-6 py-2 w-full" variant={'outline'}>
          {t('common.button.close')}
        </Button>
      </div>
    </Modal>
  );
};
