import { Button } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';
import { NoData } from '@/components/NoData';
import { Input } from '@/components/ui/input';
import { useQuery } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { ICountJobs } from '@/types/audience';
import { WarningPushAudience } from '@/components/WarningPushAudience';

type Props = {
  toggleOpen: () => void;
  onSubmitAddSegment: (payload: { audience_name: string; segment_id: string }) => void;
  loading: boolean;
};

const AddAudienceView = ({ toggleOpen, onSubmitAddSegment, loading }: Props) => {
  const { items } = useSegmentContext();
  const { adsAccount } = useTiktokContext();
  const { t } = useTranslation();
  const [openWarning, setOpenWarning] = useState<boolean>(false);
  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string; count: number }[]>(
    (acc, item) => {
      if (item.contact_quantity > 0) {
        acc.push({
          label: item.name,
          value: item.id,
          count: item.contact_quantity,
        });
      }
      return acc;
    },
    [],
  );
  const segmentSelected = items.find((item) => item.id === payload.segment_id);

  const { data: countJobs, isLoading: loadingCountJob } = useQuery({
    queryKey: [QUERY_KEY.CUSTOM_AUDIENCE_COUNT, payload.segment_id],
    enabled: !!payload.segment_id,
    staleTime: 1000,
    queryFn: () =>
      get<ICountJobs>({
        endpoint: ENDPOINTS.custom_audience.countJobs,
        params: {
          segment_id: payload.segment_id,
          ad_account_id: adsAccount?.ad_account_id,
        },
      }),
  });

  const handleSubmit = () => {
    onSubmitAddSegment({
      ...payload,
      audience_name: (payload?.audience_name || segmentSelected?.name) ?? '',
    });
  };

  const countSegmentPushed = countJobs?.data?.data?.count ?? 0;

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center flex-col">
        <span className="text-lg leading-8 font-medium tracking-[0.6px]">
          {t('audience.addCustomAudience')}
        </span>
        <span className="text-secondary text-sm">
          {t('audience.chooseSegment', { social: 'Tiktok' })}
        </span>
      </div>
      <div className="flex flex-col">
        <LabelCustom
          isRequire={true}
          className="mb-1"
          label={t('common.facebookAds.audiences.segment')}
        />
        <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
          <SelectTrigger className="w-full h-10 rounded-xl">
            <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
          </SelectTrigger>
          <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
            {!!newOptions.length ? (
              newOptions.map((item) => (
                <SelectItem
                  className={cn(
                    'text-sm p-2 rounded-md cursor-pointer',
                    item.value === payload.segment_id &&
                      '!bg-brand text-white hover:text-white focus:text-white',
                  )}
                  key={item.value}
                  value={item.value}
                >
                  <p>
                    {item.label}
                    <span className="text-xs text-big360Color-neutral-500">
                      {' - '}
                      {Number(item.count).toLocaleString()} {t('common.contacts')}
                    </span>
                  </p>
                </SelectItem>
              ))
            ) : (
              <NoData />
            )}
          </SelectContent>
        </Select>
      </div>
      <div className="flex flex-col">
        <LabelCustom className="mb-1" label={t('audience.customAudienceName')} />
        <Input
          value={payload.audience_name ?? ''}
          onChange={(e) =>
            setPayload((prev) => ({ ...prev, audience_name: (e.target as HTMLInputElement).value }))
          }
          className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
          placeholder={t('segment.selectSegment')}
        />
        <div className="flex items-center gap-1 text-xs text-secondary mt-2">
          <RiInformation2Line size={16} />
          {t('tiktokAds.audiences.notice', {
            segmentName: segmentSelected?.name ?? 'Segment Name',
          })}
        </div>
      </div>
      <div className="flex items-end justify-end gap-3">
        <Button
          onClick={toggleOpen}
          className="px-3 py-1 rounded-xl"
          variant={'secondary'}
          size={'lg'}
        >
          {t('common.button.cancel')}
        </Button>
        <Button
          onClick={() => {
            if (countSegmentPushed >= 1) {
              setOpenWarning(true);
            } else {
              handleSubmit();
            }
          }}
          disabled={loadingCountJob || !segmentSelected?.id}
          className="px-3 py-1 rounded-xl min-w-[144px]"
          size={'lg'}
        >
          {loading || loadingCountJob ? (
            <RiLoader2Line className="animate-spin" />
          ) : (
            t('tiktokAds.pushToTiktokAds')
          )}
        </Button>
      </div>
      <WarningPushAudience
        open={openWarning}
        setOpen={setOpenWarning}
        count={countSegmentPushed}
        submit={handleSubmit}
      />
    </div>
  );
};
export default AddAudienceView;
