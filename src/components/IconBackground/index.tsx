import React from 'react';
import { cn } from '@/lib/utils';

interface IIconBackground {
  type: 'warning' | 'error' | 'success' | 'info';
  icon: React.ReactNode;
}

export const IconBackground: React.FC<IIconBackground> = ({ type, icon }) => {
  return <IconContainer type={type} icon={icon} />;
};

const IconContainer: React.FC<IIconBackground> = ({ type, icon }) => {
  const className = () => {
    switch (type) {
      case 'warning':
        return 'bg-big360Color-warning-50 text-big360Color-warning-400';
      case 'error':
        return 'bg-big360Color-danger-50 text-big360Color-danger-500';
      case 'success':
        return 'bg-big360Color-success-50 text-big360Color-success-500';
      case 'info':
        return 'bg-big360Color-info-50 text-big360Color-info-500';
    }
  };
  return (
    <div
      className={cn(
        'w-[72px] h-[72px] rounded-full flex items-center justify-center m-auto',
        className(),
      )}
    >
      {icon}
    </div>
  );
};
