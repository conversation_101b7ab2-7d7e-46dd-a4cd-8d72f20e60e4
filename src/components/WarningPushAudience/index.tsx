import Modal from '@/components/Modal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { RiAlertLine } from '@remixicon/react';
import { Box } from '../Box';
import { IconBackground } from '@/components/IconBackground';

interface IWarningPushAudience {
  open: boolean;
  setOpen: (open: boolean) => void;
  count?: number;
  submit?: () => void;
}

export const WarningPushAudience: React.FC<IWarningPushAudience> = (
  props: IWarningPushAudience,
) => {
  const { open, setOpen, count, submit } = props;
  const { t } = useTranslation();

  return (
    <Modal
      openModal={open}
      onCloseModal={setOpen}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="max-w-[560px] h-[272px]"
      titleAlign={'center'}
      title={<IconBackground type={'warning'} icon={<RiAlertLine size={32} />} />}
    >
      <div className="flex flex-col items-center gap-4">
        <div className="text-center">
          <p className="font-medium text-lg mb-1">{t('tiktokAds.existsSegment')}</p>
          <p className="text-center text-sm text-secondary max-w-md whitespace-pre-line">
            {t('audience.warningMessagePushToTiktok', {
              count: count ?? 0,
              countTime: !!count && count > 1 ? 's' : '',
            })}
          </p>
        </div>
        <Box className="gap-2">
          <Button
            onClick={() => setOpen(false)}
            className="px-6 py-2 rounded-xl w-[250px]"
            variant={'outline'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button
            onClick={() => {
              setOpen(false);
              if (submit) {
                submit();
              }
            }}
            className="px-6 py-2 rounded-xl w-[250px]"
            variant={'primary'}
          >
            {t('common.button.confirm')}
          </Button>
        </Box>
      </div>
    </Modal>
  );
};
