export const tiktokAds = {
  title: 'TikTok Ads',

  switchAccount: 'Switch Account',

  description: 'Create and manage custom audiences for your TikTok advertising campaigns.',

  pushToTiktokAds:'Push to Tik<PERSON>',

  //   nodata
  nodata: 'You haven’t linked a Tiktok account yet.',
  nodataDescription:
    'Connect your Tiktok account to start publishing content, building Custom Audiences, generate reports and more ...',
  connect: 'Connect Tiktok',

  // select advertiser
  selectAdvertiser: 'Select advertiser',
  selectAdvertiserDescription:
    'Select your ad account that you would like to set default to CRM360.',
  selectAdvertiserNotice: 'Only available when connected to a TikTok Business Account.',

  //   create custom audience
  createCustomAudience: 'Create Custom Audience',
  createCustomAudienceDescription:
    ' Choose a Segment to create a Custom Audience for your Tiktok Ads account',
  createCustomAudienceTooltip:
    'The Custom Audience name defaults to the Segment name if not entered.',

  //   disconnect
  disconnectTitle: 'Disconnect Tiktok Ads account?',
  disconnectDescription:
    'This will remove your Tiktok Ads connection from Big360.\nYou won’t be able to publish, sync audiences, or view reports until you reconnect.',

  account: {
    title: 'Select Tiktok Ads Account',
    description: 'Select your ad account that you would like to set default to CRM360.',
  },

  pickADate: 'Pick a date',

  goToTiktokAds: 'Go to Tiktok Ads',

  audiences: {
    notice: 'If left blank, the Custom Audience name will default to "{{segmentName}}"',
  },

  accessDenied: 'Access Denied',
  accessDeniedDescription:
    "You don't have the required permissions to access TikTok Audience Management. Please contact your TikTok Ads account administrator to grant you the necessary permissions.",

  requiredPermission: 'Required Permissions:',
  audienceManagement: 'Audience Management',
  reConnect: 'Re-connect',

  somethingWentWrong: 'Something went wrong',
  somethingWentWrongDescription:
    "We're experiencing technical difficulties connecting to TikTok Ads. This could be due to a temporary service outage or network issue.",

  createAnyway: 'Create anyway',

  existsSegment: 'This segment already exists',
  updateFrequency: 'Update frequency restriction'
};
