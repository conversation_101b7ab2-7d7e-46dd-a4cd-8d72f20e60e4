#!/bin/bash

# Script để chạy TikTok Ads tests với các options khác nhau

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if dev server is running
check_dev_server() {
    if curl -s http://localhost:5173 > /dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to start dev server
start_dev_server() {
    print_status "Starting development server..."
    yarn dev &
    DEV_SERVER_PID=$!
    
    # Wait for server to start
    for i in {1..30}; do
        if check_dev_server; then
            print_success "Development server started successfully"
            return 0
        fi
        sleep 2
    done
    
    print_error "Failed to start development server"
    return 1
}

# Function to stop dev server
stop_dev_server() {
    if [ ! -z "$DEV_SERVER_PID" ]; then
        print_status "Stopping development server..."
        kill $DEV_SERVER_PID 2>/dev/null || true
        wait $DEV_SERVER_PID 2>/dev/null || true
        print_success "Development server stopped"
    fi
}

# Function to run specific test suite
run_test_suite() {
    local test_type=$1
    local browser=${2:-"electron"}
    
    case $test_type in
        "all")
            print_status "Running all TikTok Ads tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/**/*.cy.{js,jsx,ts,tsx}" --browser $browser
            ;;
        "auth")
            print_status "Running authentication tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/authentication.cy.ts" --browser $browser
            ;;
        "page")
            print_status "Running page tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/tiktok-ads-page.cy.ts" --browser $browser
            ;;
        "audiences")
            print_status "Running custom audiences tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/custom-audiences.cy.ts" --browser $browser
            ;;
        "update")
            print_status "Running update audience tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/update-audience.cy.ts" --browser $browser
            ;;
        "account")
            print_status "Running account selection tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/account-selection.cy.ts" --browser $browser
            ;;
        "filter")
            print_status "Running filter panel tests..."
            yarn cypress run --spec "cypress/e2e/tiktok-ads/filter-panel.cy.ts" --browser $browser
            ;;
        "component")
            print_status "Running component tests..."
            yarn cypress run --component --spec "cypress/component/tiktok-ads/**/*.cy.{js,jsx,ts,tsx}"
            ;;
        *)
            print_error "Unknown test suite: $test_type"
            print_status "Available options: all, auth, page, audiences, update, account, filter, component"
            exit 1
            ;;
    esac
}

# Function to show help
show_help() {
    echo "TikTok Ads Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS] [TEST_SUITE] [BROWSER]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -o, --open     Open Cypress GUI instead of running headless"
    echo "  -s, --server   Start dev server automatically"
    echo "  -v, --verbose  Enable verbose output"
    echo ""
    echo "Test Suites:"
    echo "  all            Run all TikTok Ads E2E tests (default)"
    echo "  auth           Run authentication tests"
    echo "  page           Run main page tests"
    echo "  audiences      Run custom audiences tests"
    echo "  update         Run update audience tests (includes time validation)"
    echo "  account        Run account selection tests"
    echo "  filter         Run filter panel tests"
    echo "  component      Run component tests"
    echo ""
    echo "Browsers:"
    echo "  electron       Default Cypress browser"
    echo "  chrome         Google Chrome"
    echo "  firefox        Mozilla Firefox"
    echo "  edge           Microsoft Edge"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run all tests with electron"
    echo "  $0 update chrome             # Run update tests with Chrome"
    echo "  $0 -o                        # Open Cypress GUI"
    echo "  $0 -s all firefox            # Start server and run all tests with Firefox"
    echo "  $0 component                 # Run component tests"
}

# Parse command line arguments
OPEN_GUI=false
START_SERVER=false
VERBOSE=false
TEST_SUITE="all"
BROWSER="electron"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--open)
            OPEN_GUI=true
            shift
            ;;
        -s|--server)
            START_SERVER=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            if [ -z "$TEST_SUITE_SET" ]; then
                TEST_SUITE=$1
                TEST_SUITE_SET=true
            elif [ -z "$BROWSER_SET" ]; then
                BROWSER=$1
                BROWSER_SET=true
            else
                print_error "Unknown argument: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Main execution
main() {
    print_status "TikTok Ads Test Runner Starting..."
    
    # Check if yarn is available
    if ! command -v yarn &> /dev/null; then
        print_error "yarn is not installed. Please install yarn first."
        exit 1
    fi
    
    # Check if Cypress is installed
    if ! yarn cypress version &> /dev/null; then
        print_error "Cypress is not installed. Please run 'yarn install' first."
        exit 1
    fi
    
    # Start dev server if requested
    if [ "$START_SERVER" = true ]; then
        if ! check_dev_server; then
            start_dev_server
            SERVER_STARTED=true
        else
            print_warning "Development server is already running"
        fi
    else
        if ! check_dev_server; then
            print_warning "Development server is not running. Tests may fail."
            print_status "Use -s flag to start server automatically, or run 'yarn dev' manually."
        fi
    fi
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Run tests
    if [ "$OPEN_GUI" = true ]; then
        print_status "Opening Cypress GUI..."
        yarn cypress open
    else
        run_test_suite "$TEST_SUITE" "$BROWSER"
        
        if [ $? -eq 0 ]; then
            print_success "All tests passed!"
        else
            print_error "Some tests failed!"
            exit 1
        fi
    fi
}

# Cleanup function
cleanup() {
    if [ "$SERVER_STARTED" = true ]; then
        stop_dev_server
    fi
}

# Run main function
main
