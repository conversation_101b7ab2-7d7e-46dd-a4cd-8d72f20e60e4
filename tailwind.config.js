/** @type {import('tailwindcss').Config} */

const big360Color = {
  neutral: {
    '00': '#FDFDFD',
    '50': '#F5F5F5',
    '100': '#E6E7E9',
    '200': '#D9DBDE',
    '300': '#C5C8CB',
    '400': '#ADB0B4',
    '500': '#909498',
    '600': '#6F7377',
    '700': '#4E5255',
    '800': '#2E3133',
    '900': '#1E1F21',
    '950': '#141416',
  },
  neutralDark: {
    '00': '#141416',
    '50': '#1A1B1D',
    '100': '#212224',
    '200': '#2A2C2F',
    '300': '#36383C',
    '400': '#46484D',
    '500': '#5A5D63',
    '600': '#707479',
    '700': '#8A8E93',
    '800': '#AEB2B6',
    '900': '#D3D6D9',
    '950': '#FDFDFD',
  },
  brand: {
    '00': '#F4F0FF',
    '50': '#E3DAFF',
    '100': '#CFBEFF',
    '200': '#B89EFF',
    '300': '#A37EFF',
    '400': '#8F5CFF',
    '500': '#7C47E6',
    '600': '#6A3CC2',
    '700': '#582FA0',
    '800': '#45257F',
    '900': '#331B5F',
    '950': '#241344',
  },
  brandAlt: {
    '00': '#341273',
    '50': '#42139D',
    '100': '#4F12C9',
    '200': '#5D14F2',
    '300': '#7537F9',
    '400': '#8F5CFF',
    '500': '#9F75FF',
    '600': '#AF8DFF',
    '700': '#C0A6FF',
    '800': '#D1BFFF',
    '900': '#E2D7FF',
    '950': '#F4F0FF',
  },
  success: {
    '00': '#F3FDF7',
    '50': '#E3F8EF',
    '100': '#C4F1DD',
    '200': '#97E6C4',
    '300': '#6CDCAD',
    '400': '#45D29C',
    '500': '#2BB684',
    '600': '#22996F',
    '700': '#197A59',
    '800': '#135D45',
    '900': '#0E4634',
    '950': '#0A3326',
  },
  successAlt: {
    '00': '#0A3326',
    '50': '#0E4634',
    '100': '#135D45',
    '200': '#197A59',
    '300': '#22996F',
    '400': '#2BB684',
    '500': '#45D29C',
    '600': '#6CDCAD',
    '700': '#97E6C4',
    '800': '#C4F1DD',
    '900': '#E3F8EF',
    '950': '#F3FDF7',
  },
  info: {
    '00': '#F2F8FF',
    '50': '#E4F0FE',
    '100': '#CBE3FD',
    '200': '#A6D3FC',
    '300': '#84C3FB',
    '400': '#69B4FA',
    '500': '#4F9EF0',
    '600': '#4284D0',
    '700': '#366BB0',
    '800': '#2A5490',
    '900': '#1E4071',
    '950': '#152E55',
  },
  infoAlt: {
    '00': '#152E55',
    '50': '#1E4071',
    '100': '#2A5490',
    '200': '#366BB0',
    '300': '#4284D0',
    '400': '#4F9EF0',
    '500': '#69B4FA',
    '600': '#84C3FB',
    '700': '#A6D3FC',
    '800': '#CBE3FD',
    '900': '#E4F0FE',
    '950': '#F2F8FF',
  },
  warning: {
    '00': '#FFFBE8',
    '50': '#FFF5D6',
    '100': '#FFE89E',
    '200': '#FFD75A',
    '300': '#FFC930',
    '400': '#FFBC00',
    '500': '#F5B200',
    '600': '#D89D00',
    '700': '#BB8700',
    '800': '#9E7200',
    '900': '#835D00',
    '950': '#684A00',
  },
  warningAlt: {
    '00': '#684A00',
    '50': '#835D00',
    '100': '#9E7200',
    '200': '#BB8700',
    '300': '#D89D00',
    '400': '#F5B200',
    '500': '#FFBC00',
    '600': '#FFC930',
    '700': '#FFD75A',
    '800': '#FFE89E',
    '900': '#FFF5D6',
    '950': '#FFFBE8',
  },
  danger: {
    '00': '#FFF4F4',
    '50': '#FFE7E7',
    '100': '#FFCECE',
    '200': '#FFA3A3',
    '300': '#FF7A7A',
    '400': '#FF5C5C',
    '500': '#F84242',
    '600': '#DB3838',
    '700': '#BF2D2D',
    '800': '#A32121',
    '900': '#891717',
    '950': '#6E0F0F',
  },
  dangerAlt: {
    '00': '#6E0F0F',
    '50': '#891717',
    '100': '#A32121',
    '200': '#BF2D2D',
    '300': '#DB3838',
    '400': '#F84242',
    '500': '#FF5C5C',
    '600': '#FF7A7A',
    '700': '#FFA3A3',
    '800': '#FFCECE',
    '900': '#FFE7E7',
    '950': '#FFF4F4',
  },
  alphaWhite: {
    '00': '#FDFDFD00',
    '08': '#FDFDFD14',
    '12': '#FDFDFD1F',
    '16': '#FDFDFD29',
    '24': '#FDFDFD3D',
    '32': '#FDFDFD52',
    '48': '#FDFDFD7A',
    '56': '#FDFDFD8F',
    '64': '#FDFDFDA3',
    '72': '#FDFDFDB8',
    '80': '#FDFDFDCC',
    '96': '#FDFDF8F5',
  },
  alphaBlack: {
    '00': '#1E1F2100',
    '08': '#1E1F2114',
    '12': '#1E1F211F',
    '16': '#1E1F2129',
    '24': '#1E1F213D',
    '32': '#1E1F2152',
    '48': '#1E1F217A',
    '56': '#1E1F218F',
    '64': '#1E1F21A3',
    '72': '#1E1F21B8',
    '80': '#1E1F21CC',
    '88': '#1E1F21E0',
  },
  alphaBrand: {
    '08': '#8F5CFF14',
    '12': '#8F5CFF1F',
    '16': '#8F5CFF29',
    '24': '#8F5CFF3D',
    '32': '#8F5CFF52',
    '48': '#8F5CFF7A',
    '56': '#8F5CFF8F',
    '64': '#8F5CFFA3',
    '72': '#8F5CFFB8',
    '80': '#8F5CFFCC',
    '96': '#8F5CFFF5',
  },
};


export default {
  darkMode: ['class'],
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      animation: {
        loadingDotOne: 'loadingDotOne 1s infinite',
        loadingDotTwo: 'loadingDotTwo 1.1s infinite',
        loadingDotThree: 'loadingDotThree 1.2s infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      keyframes: {
        loadingDotOne: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        loadingDotTwo: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        loadingDotThree: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      boxShadow: {
        filter: 'var(--shadow-filter)',
        medium: '0px 4px 20px -8px hsla(225, 18%, 4%, 0.1)',
        light: '0px 0px 32px 0px hsla(225, 18%, 4%, 0.02)',
      },
      colors: {
        brand: {
          DEFAULT: big360Color.brand['400'], // Tương ứng với --brand: 259, 100%, 68%
          foreground: big360Color.brand['200'], // Tương ứng với --brand-foreground: 257, 100%, 75%
          secondary: big360Color.brand['100'], // Tương ứng với --brand-secondary: 256, 100%, 76%
        },
        background: {
          DEFAULT: big360Color.neutral['00'], // Tương ứng với --background: 0 0% 100%
          foreground: big360Color.neutral['50'], // Tương ứng với --background-foreground: 0 0% 99%
        },
        foreground: big360Color.neutral['950'], // Tương ứng với --foreground: 0 0% 3.9%
        card: {
          DEFAULT: big360Color.neutral['00'], // Tương ứng với --card: 0 0% 100%
          foreground: big360Color.neutral['950'], // Tương ứng với --card-foreground: 0 0% 3.9%
        },
        popover: {
          DEFAULT: big360Color.neutral['00'], // Tương ứng với --popover: 0 0% 100%
          foreground: big360Color.neutral['950'], // Tương ứng với --popover-foreground: 0 0% 3.9%
        },
        tertiary: {
          DEFAULT: big360Color.neutral['100'], // Tương ứng với --tertiary: 210, 3%, 89%
          foreground: big360Color.neutral['700'], // Tương ứng với --tertiary-foreground: 226, 12%, 36%
        },
        primary: {
          crm: big360Color.neutral['900'], // Tương ứng với --primary-crm: 225, 16%, 15%
          foreground: big360Color.neutral['50'], // Tương ứng với --primary-foreground-crm: 0 0% 98%
        },
        text: {
          primary: big360Color.brand['700'], // Tương ứng với --text-primary-crm: 264, 78%, 42%
          foreground: big360Color.brand['50'], // Tương ứng với --text-foreground: 253, 100%, 93%
        },
        secondary: {
          DEFAULT: big360Color.neutral['600'], // Tương ứng với --secondary: 206, 4%, 32%
          foreground_crm: big360Color.neutral['50'], // Tương ứng với --secondary-foreground-crm: 0, 0%, 94%
        },
        muted: {
          DEFAULT: big360Color.neutral['50'], // Tương ứng với --muted: 0 0% 96.1%
          foreground: big360Color.neutral['500'], // Tương ứng với --muted-foreground: 0 0% 45.1%
        },
        accent: {
          DEFAULT: big360Color.neutral['50'], // Tương ứng với --accent: 0 0% 96.1%
          foreground: big360Color.neutral['900'], // Tương ứng với --accent-foreground: 0 0% 9%
        },
        destructive: {
          DEFAULT: big360Color.danger['400'], // Tương ứng với --destructive: 0 84.2% 60.2%
          foreground: big360Color.neutral['50'], // Tương ứng với --destructive-foreground: 0 0% 98%
        },
        create: {
          DEFAULT: big360Color.neutral['900'], // Tương ứng với --create: 227, 18%, 10%
          foreground: big360Color.neutral['700'], // Tương ứng với --create-foreground: 227, 14%, 25%
        },
        error: {
          DEFAULT: big360Color.success['50'], // Tương ứng với --error: 0, 100%, 93%
          text: big360Color.danger['700'], // Tương ứng với --error-text: 0, 79%, 42%
        },
        success: {
          DEFAULT: big360Color.success['50'], // Tương ứng với --success: 128, 63%, 93%
          hover: big360Color.success['600'], // Tương ứng với --success-hover: 131, 58%, 36%
          text: big360Color.success['800'], // Tương ứng với --success-text: 131, 48%, 24%
        },
        standard: {
          DEFAULT: big360Color.info['50'], // Tương ứng với --standard: 201, 100%, 97%
          text: big360Color.info['600'], // Tương ứng với --standard-text: 215, 84%, 48%
        },
        disabled: {
          DEFAULT: big360Color.neutral['400'], // Tương ứng với --disabled: 222, 6%, 67%
        },
        filter: {
          DEFAULT: big360Color.neutral['600'], // Tương ứng với --filter-text: 225, 10%, 47%
        },
        delete: {
          DEFAULT: big360Color.danger['400'], // Tương ứng với --delete: 0, 90%, 60%
          foreground: big360Color.danger['100'], // Tương ứng với --delete-foreground: 0, 100%, 82%
        },
        warning: {
          DEFAULT: big360Color.warning['500'], // Tương ứng với --warning: 46, 96%, 56%
          background: big360Color.warning['50'], // Tương ứng với --warning-bg: 51, 100%, 96%
          text: big360Color.warning['800'], // Tương ứng với --warning-text: 26, 82%, 31%
        },
        info: {
          DEFAULT: big360Color.info['500'], // Tương ứng với --info: 208, 100%, 59%
          background: big360Color.info['50'], // Tương ứng với --info-bg: 201, 100%, 97%
          text: big360Color.info['800'], // Tương ứng với --info-text: 215, 70%, 33%
        },
        avatar: {
          DEFAULT: big360Color.neutral['300'], // Tương ứng với --avatar: 0, 0%, 85%
        },
        hover: {
          filter: big360Color.brand['600'], // Tương ứng với --hover-filter: 266, 78%, 36%
          foreground: big360Color.brand['50'], // Tương ứng với --hover-filter-foreground: 268, 78%, 93%
          table: big360Color.neutral['50'], // Tương ứng với --cell-hover: 240, 8%, 97%
        },
        border: big360Color.neutral['100'], // Tương ứng với --border: 210, 3%, 89%
        input: big360Color.neutral['200'], // Tương ứng với --input: 0 0% 89.8%
        ring: big360Color.neutral['950'], // Tương ứng với --ring: 0 0% 3.9%
        chart: {
          1: big360Color.danger['400'], // Tương ứng với --chart-1: 12 76% 61%
          2: big360Color.success['600'], // Tương ứng với --chart-2: 173 58% 39%
          3: big360Color.neutral['800'], // Tương ứng với --chart-3: 197 37% 24%
          4: big360Color.warning['400'], // Tương ứng với --chart-4: 43 74% 66%
          5: big360Color.warning['600'], // Tương ứng với --chart-5: 27 87% 67%
        },
        overlay: {
          DEFAULT: big360Color.alphaBlack['32'], // Tương ứng với --overlay: rgba(9, 10, 13, 0.35)
          box: {
            icon: big360Color.alphaBlack['32'], // Tương ứng với --box-icon-overlay: rgba(9, 10, 13, 0.40)
          },
        },
        big360Color
      },
      plugins: ['tailwindcssAnimate'],
    },
  },
};
