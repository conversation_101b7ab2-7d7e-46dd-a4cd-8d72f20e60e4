{"name": "crm360-react-application", "private": true, "version": "1.0.0", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky install", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox", "cypress:run:edge": "cypress run --browser edge", "test:e2e": "cypress run --spec 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}'", "test:e2e:tiktok": "cypress run --spec 'cypress/e2e/tiktok-ads/**/*.cy.{js,jsx,ts,tsx}'", "test:component": "cypress run --component", "test:all": "npm run test:e2e && npm run test:component"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^44.2.1", "@ckeditor/ckeditor5-react": "^9.5.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.1", "@originjs/vite-plugin-federation": "^1.3.6", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.3.0", "@remixicon/react": "^4.5.0", "@tanstack/react-query": "^5.61.3", "@tanstack/react-table": "^8.20.5", "@tanstack/react-virtual": "^3.11.0", "@types/react-icons": "^3.0.0", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "file-saver": "^2.0.5", "i18next": "^24.0.2", "input-otp": "^1.4.2", "lodash": "^4.17.21", "path": "^0.12.7", "prettier": "^3.3.3", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.3", "react-icon": "^1.0.0", "react-onesignal": "^3.0.1", "react-redux": "^9.1.2", "react-router-dom": "^6.28.0", "recharts": "^2.15.0", "redux": "^5.0.1", "redux-toolkit": "^1.1.2", "sonner": "^1.7.1", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@eslint/js": "^9.13.0", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.13", "@types/node": "^22.9.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "cypress": "^14.5.1", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "husky": "9.1.7", "install": "^0.13.0", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.4", "vite": "^5.4.11"}}